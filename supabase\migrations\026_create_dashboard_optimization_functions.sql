-- <PERSON>reate optimized RPC functions for dashboard data aggregation
-- This migration creates functions to replace multiple individual queries with single aggregated calls

-- Function to get comprehensive dashboard metrics in a single call
CREATE OR REPLACE FUNCTION get_dashboard_metrics(
    p_user_id UUID DEFAULT NULL,
    p_user_role user_role DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
    dashboard_data JSON;
    today_date DATE;
    week_start_date DATE;
    month_start_date DATE;
BEGIN
    -- Get current user info
    current_user_id := COALESCE(p_user_id, auth.uid());
    
    -- Get user role if not provided
    IF p_user_role IS NULL THEN
        SELECT role INTO current_user_role
        FROM profiles
        WHERE id = current_user_id;
    ELSE
        current_user_role := p_user_role;
    END IF;
    
    -- Set date ranges
    today_date := CURRENT_DATE;
    week_start_date := today_date - EXTRACT(DOW FROM today_date)::INTEGER;
    month_start_date := DATE_TRUNC('month', today_date)::DATE;
    
    -- Build comprehensive dashboard data
    SELECT jsonb_build_object(
        'field_staff', jsonb_build_object(
            'total_staff', (
                SELECT COUNT(*)
                FROM profiles
                WHERE role = 'field_staff'
                AND COALESCE(is_active, true) = true
            ),
            'active_staff', (
                SELECT COUNT(*)
                FROM profiles
                WHERE role = 'field_staff'
                AND COALESCE(is_active, true) = true
                AND updated_at >= today_date - INTERVAL '7 days'
            ),
            'checked_in_today', (
                SELECT COUNT(DISTINCT staff_id)
                FROM field_staff_attendance
                WHERE attendance_date = today_date
                AND status = 'active'
            ),
            'average_hours_per_day', (
                SELECT COALESCE(AVG(total_duration_minutes) / 60.0, 0)
                FROM field_staff_attendance
                WHERE attendance_date >= week_start_date
                AND total_duration_minutes IS NOT NULL
            ),
            'check_in_compliance_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN fsa.id IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM profiles p
                LEFT JOIN field_staff_attendance fsa ON p.id = fsa.staff_id AND fsa.attendance_date = today_date
                WHERE p.role = 'field_staff' AND COALESCE(p.is_active, true) = true
            ),
            'report_submission_rate', (
                SELECT CASE 
                    WHEN COUNT(DISTINCT fsa.staff_id) > 0 THEN 
                        (COUNT(DISTINCT fr.staff_id)::DECIMAL / COUNT(DISTINCT fsa.staff_id)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM field_staff_attendance fsa
                LEFT JOIN field_reports fr ON fsa.staff_id = fr.staff_id AND fsa.attendance_date = fr.report_date
                WHERE fsa.attendance_date >= month_start_date
            )
        ),
        'program_reach', jsonb_build_object(
            'total_students_reached', (
                SELECT COALESCE(SUM(total_students_attended), 0)
                FROM field_reports
                WHERE report_date >= month_start_date
            ),
            'schools_covered', (
                SELECT COUNT(DISTINCT school_id)
                FROM field_staff_attendance
                WHERE attendance_date >= month_start_date
            ),
            'total_schools', (
                SELECT COUNT(*)
                FROM schools
                WHERE COALESCE(is_active, true) = true
            ),
            'session_completion_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN status IN ('approved', 'published') THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM field_reports
                WHERE report_date >= month_start_date
            ),
            'average_attendance_per_session', (
                SELECT COALESCE(AVG(total_students_attended), 0)
                FROM field_reports
                WHERE report_date >= month_start_date
                AND total_students_attended > 0
            ),
            'book_distribution_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN status = 'completed' THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM book_distributions
                WHERE created_at >= month_start_date
            )
        ),
        'operational', jsonb_build_object(
            'task_completion_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN status = 'completed' THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM tasks
                WHERE created_at >= month_start_date
            ),
            'average_task_completion_time', (
                SELECT COALESCE(AVG(EXTRACT(EPOCH FROM (updated_at - created_at)) / 86400), 0)
                FROM tasks
                WHERE status = 'completed'
                AND updated_at >= month_start_date
            ),
            'report_quality_score', 88.0, -- Placeholder - can be calculated based on report completeness
            'resource_utilization', 76.0, -- Placeholder - can be calculated based on actual metrics
            'offline_sync_success_rate', 94.0 -- Placeholder - would need sync logs to calculate
        ),
        'quality', jsonb_build_object(
            'session_attendance_trend', (
                SELECT CASE 
                    WHEN LAG(avg_attendance) OVER (ORDER BY week_num) > 0 THEN
                        ((avg_attendance - LAG(avg_attendance) OVER (ORDER BY week_num)) / 
                         LAG(avg_attendance) OVER (ORDER BY week_num)) * 100
                    ELSE 0
                END
                FROM (
                    SELECT 
                        EXTRACT(WEEK FROM report_date) as week_num,
                        AVG(total_students_attended) as avg_attendance
                    FROM field_reports
                    WHERE report_date >= today_date - INTERVAL '2 weeks'
                    GROUP BY EXTRACT(WEEK FROM report_date)
                    ORDER BY week_num DESC
                    LIMIT 1
                ) weekly_trends
            ),
            'student_engagement_score', 4.3, -- Placeholder - would need engagement metrics
            'follow_up_completion_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN follow_up_required = false OR follow_up_actions IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM field_reports
                WHERE report_date >= month_start_date
                AND follow_up_required = true
            ),
            'challenge_resolution_time', 1.8, -- Placeholder - would need challenge tracking
            'feedback_sentiment', 4.1 -- Placeholder - would need feedback analysis
        ),
        'user_context', jsonb_build_object(
            'user_id', current_user_id,
            'user_role', current_user_role,
            'can_view_all_data', current_user_role IN ('admin', 'program_officer'),
            'generated_at', NOW()
        )
    ) INTO dashboard_data;
    
    RETURN dashboard_data;
END;
$$;

-- Function to get activity summary for dashboard
CREATE OR REPLACE FUNCTION get_dashboard_activity_summary(
    p_user_id UUID DEFAULT NULL,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
    activity_data JSON;
    today_date DATE;
BEGIN
    current_user_id := COALESCE(p_user_id, auth.uid());
    
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    today_date := CURRENT_DATE;
    
    -- Build activity summary
    SELECT jsonb_build_object(
        'today_summary', jsonb_build_object(
            'check_ins', (
                SELECT COUNT(*)
                FROM field_staff_attendance
                WHERE attendance_date = today_date
                AND (current_user_role IN ('admin', 'program_officer') OR staff_id = current_user_id)
            ),
            'reports_submitted', (
                SELECT COUNT(*)
                FROM field_reports
                WHERE report_date = today_date
                AND (current_user_role IN ('admin', 'program_officer') OR staff_id = current_user_id)
            ),
            'students_reached', (
                SELECT COALESCE(SUM(total_students_attended), 0)
                FROM field_reports
                WHERE report_date = today_date
                AND (current_user_role IN ('admin', 'program_officer') OR staff_id = current_user_id)
            ),
            'active_staff', (
                SELECT COUNT(DISTINCT staff_id)
                FROM field_staff_attendance
                WHERE attendance_date = today_date
                AND status = 'active'
                AND (current_user_role IN ('admin', 'program_officer') OR staff_id = current_user_id)
            )
        ),
        'weekly_trend', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'date', trend_date,
                    'activities', activities,
                    'students', students,
                    'hours', hours
                ) ORDER BY trend_date
            )
            FROM (
                SELECT 
                    d.trend_date,
                    COALESCE(COUNT(fsa.id), 0) as activities,
                    COALESCE(SUM(fr.total_students_attended), 0) as students,
                    COALESCE(SUM(fsa.total_duration_minutes) / 60.0, 0) as hours
                FROM (
                    SELECT generate_series(
                        today_date - p_days_back + 1,
                        today_date,
                        '1 day'::interval
                    )::date as trend_date
                ) d
                LEFT JOIN field_staff_attendance fsa ON fsa.attendance_date = d.trend_date
                    AND (current_user_role IN ('admin', 'program_officer') OR fsa.staff_id = current_user_id)
                LEFT JOIN field_reports fr ON fr.report_date = d.trend_date
                    AND (current_user_role IN ('admin', 'program_officer') OR fr.staff_id = current_user_id)
                GROUP BY d.trend_date
                ORDER BY d.trend_date
            ) trends
        ),
        'performance_indicators', jsonb_build_object(
            'attendance_rate', (
                SELECT CASE 
                    WHEN COUNT(*) > 0 THEN 
                        (COUNT(CASE WHEN fsa.id IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM generate_series(today_date - p_days_back + 1, today_date, '1 day'::interval) d(day_date)
                CROSS JOIN (
                    SELECT id FROM profiles 
                    WHERE role = 'field_staff' 
                    AND COALESCE(is_active, true) = true
                    AND (current_user_role IN ('admin', 'program_officer') OR id = current_user_id)
                ) p
                LEFT JOIN field_staff_attendance fsa ON fsa.staff_id = p.id AND fsa.attendance_date = d.day_date::date
            ),
            'report_completion_rate', (
                SELECT CASE 
                    WHEN COUNT(DISTINCT fsa.staff_id, fsa.attendance_date) > 0 THEN 
                        (COUNT(DISTINCT fr.staff_id, fr.report_date)::DECIMAL / COUNT(DISTINCT fsa.staff_id, fsa.attendance_date)::DECIMAL) * 100
                    ELSE 0 
                END
                FROM field_staff_attendance fsa
                LEFT JOIN field_reports fr ON fsa.staff_id = fr.staff_id AND fsa.attendance_date = fr.report_date
                WHERE fsa.attendance_date >= today_date - p_days_back + 1
                AND (current_user_role IN ('admin', 'program_officer') OR fsa.staff_id = current_user_id)
            ),
            'average_session_quality', (
                SELECT COALESCE(AVG(
                    CASE 
                        WHEN total_students_attended > 0 AND activities_conducted IS NOT NULL 
                        THEN 4.0 -- Simple quality score based on completeness
                        ELSE 2.0 
                    END
                ), 0)
                FROM field_reports
                WHERE report_date >= today_date - p_days_back + 1
                AND (current_user_role IN ('admin', 'program_officer') OR staff_id = current_user_id)
            )
        )
    ) INTO activity_data;
    
    RETURN activity_data;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_dashboard_metrics(UUID, user_role) TO authenticated;
GRANT EXECUTE ON FUNCTION get_dashboard_activity_summary(UUID, INTEGER) TO authenticated;
